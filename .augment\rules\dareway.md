---
type: "always_apply"
---

我将会给你一些功能描述，你生成一份**静态 HTML 文件**（使用 `template.html` 的相同样式）：

### 样式与主题

1. **整体主题色**：`#31969A`
2. **配色方案**：

   * 主色：`#31969A`
   * 辅助色：`#80CCE3`、`#8199C7`、`#E5CE66`
3. **页面要求**：

   * 无副标题（导航栏不显示标题）
   * 使用简洁、清晰的管理可视化风格

### 表格要求

1. 表格数据不少于 **5 条**，内容仿照真实数据。
2. 表格需包含所有功能
3. 表格必须有**完整边框**（上下左右及内部线），并在表格底部添加**分页信息**（如 “共 5 条，每页显示 5 条，第 1 页/共 1 页”，不允许出现只展示了4条数据，且写出每页显示 5 条，第 1 页/共 12页这种错误情况）

### 数据要求
1. 数据内容仿照真实数据
2. 表格的每一个单元格数据尽量不要重复，如果要重复，不可重复3次以上
###项目信息
以“电力数据挖掘+政策环境研究”双轮驱动，紧扣2025年中央一号文件提出的“持续巩固拓展脱贫攻坚成果”等重要政策刚需，围绕乡村振兴发展战略和“不发生规模性返贫”底线要求，以“产业振兴评估”和“防返贫监测”为主线价值，以电力数据为视角，建立重点分析层级和监测体系，透视重庆市当前重点监测县/村脱贫和防返贫成效，并为后续乡村振兴工作提供科学依据。

基于能源电力环境动态感知与战略辅助决策方法，结合经济活动分析和区域能级协同理论，开展优势特色产业定位分析和优势特色产业发展趋势分析。依据重点监测县各产业用电数据，定位各县特色优势产业，分析脱贫县产业扶贫成效，并根据产业用电变化趋势，分析预测重点脱贫县产业发展趋势，评估产业振兴水平和帮扶效能。
### 其他要求
只有纯粹的数据展示页面才可能需要如（优秀村庄42 个）这种数据展示卡片，一旦功能复杂，不允许加任何数据展示卡片


### 输出

生成完整的、可直接打开的 HTML 文件,符合模板样式与上述数据要求，文件名为x.x.x.x-{{标题名称}}
