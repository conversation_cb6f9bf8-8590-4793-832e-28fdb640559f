<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>脱贫县产业扶贫成效评估</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#31969A',
            secondary: '#80CCE3',
            accent: '#8199C7',
            warning: '#E5CE66',
            danger: '#E74C3C',
            light: '#F8FAFC',
            lighter: '#FFFFFF',
            border: '#E2E8F0',
            grid: 'rgba(226, 232, 240, 0.5)',
            text: {
              primary: '#1E293B',
              secondary: '#64748B',
              tertiary: '#94A3B8'
            }
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
        },
      }
    }
  </script>
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .card-shadow {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
      }
      .chart-container {
        position: relative;
        height: 350px;
        width: 100%;
      }
      .scrollbar-thin {
        scrollbar-width: thin;
      }
      .scrollbar-thin::-webkit-scrollbar {
        width: 6px;
      }
      .scrollbar-thin::-webkit-scrollbar-thumb {
        background-color: rgba(100, 116, 139, 0.3);
        border-radius: 3px;
      }
      .stat-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(49, 150, 154, 0.1);
      }
    }
  </style>
</head>

<body class="bg-light font-inter text-text-primary min-h-screen">
  <!-- 顶部导航栏 -->
  <header class="bg-lighter/90 backdrop-blur-md border-b border-border sticky top-0 z-50 transition-all duration-300">
    <div class="container mx-auto px-6 py-3 flex justify-between items-center">
      <div class="flex items-center space-x-3">
        <i class="fa fa-line-chart text-primary text-3xl"></i>
      </div>
      <div class="flex items-center space-x-4">
        <div class="bg-light/70 border border-border rounded-lg px-4 py-2 flex items-center">
          <i class="fa fa-calendar-o mr-2 text-text-secondary"></i>
          <span id="current-date-display" class="text-text-primary">2025年8月5日</span>
        </div>
      </div>
    </div>
  </header>

  <div class="container mx-auto px-6 py-6">
    <!-- 总体指标卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">脱贫县总数</p>
            <h3 class="text-3xl font-bold mt-2">18 <span class="text-lg font-normal text-text-secondary">个</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center text-primary">
            <i class="fa fa-map-marker text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-primary flex items-center">
            <i class="fa fa-check mr-1"></i> 全覆盖
          </span>
          <span class="text-text-secondary ml-2">监测覆盖</span>
        </div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">产业帮扶项目数量</p>
            <h3 class="text-3xl font-bold mt-2">342 <span class="text-lg font-normal text-text-secondary">个</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-secondary/10 flex items-center justify-center text-secondary">
            <i class="fa fa-briefcase text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-primary flex items-center">
            <i class="fa fa-arrow-up mr-1"></i> 15.2%
          </span>
          <span class="text-text-secondary ml-2">较去年</span>
        </div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">脱贫人口就业率</p>
            <h3 class="text-3xl font-bold mt-2">92.8 <span class="text-lg font-normal text-text-secondary">%</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-accent/10 flex items-center justify-center text-accent">
            <i class="fa fa-users text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-primary flex items-center">
            <i class="fa fa-arrow-up mr-1"></i> 3.4%
          </span>
          <span class="text-text-secondary ml-2">较去年</span>
        </div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">防返贫预警数</p>
            <h3 class="text-3xl font-bold mt-2">12 <span class="text-lg font-normal text-text-secondary">户</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-warning/10 flex items-center justify-center text-warning">
            <i class="fa fa-exclamation-triangle text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-danger flex items-center">
            <i class="fa fa-arrow-down mr-1"></i> 28.6%
          </span>
          <span class="text-text-secondary ml-2">较去年</span>
        </div>
      </div>
    </div>

    <!-- 控制面板 -->
    <div class="bg-lighter border border-border rounded-xl p-6 mb-6 card-shadow">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">管理单位</label>
          <div class="relative">
            <select id="management-unit" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="all">全部单位</option>
              <option value="unit1">奉节县农业农村委</option>
              <option value="unit2">巫溪县农业农村委</option>
              <option value="unit3">城口县农业农村委</option>
              <option value="unit4">酉阳县农业农村委</option>
              <option value="unit5">秀山县农业农村委</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">日期范围</label>
          <div class="flex space-x-3">
            <input type="date" id="start-date" class="flex-1 bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
            <input type="date" id="end-date" class="flex-1 bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">操作</label>
          <div class="flex space-x-3">
            <button id="btn-search" class="flex-1 px-4 py-3 bg-primary text-white rounded-lg border border-primary hover:bg-primary/90 transition-colors">
              <i class="fa fa-search mr-1"></i> 查询
            </button>
            <button id="btn-download-report" class="px-4 py-3 bg-primary/20 text-primary rounded-lg border border-primary/30 hover:bg-primary/30 transition-colors">
              <i class="fa fa-download mr-1"></i> 下载报告
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 人均用电量对比图表 -->
    <div class="bg-lighter border border-border rounded-xl p-6 mb-6 card-shadow">
      <h3 class="text-xl font-semibold text-text-primary mb-4">脱贫县本年度与上年度人均用电量对比</h3>
      <div class="chart-container">
        <canvas id="power-comparison-chart"></canvas>
      </div>
    </div>

    <!-- 脱贫县产业扶贫成效表格 -->
    <div class="bg-lighter border border-border rounded-xl p-6 card-shadow">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold text-text-primary">脱贫县产业扶贫成效明细</h3>
      </div>
      
      <div class="overflow-x-auto">
        <table class="w-full border-collapse border border-border">
          <thead>
            <tr class="bg-light">
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">脱贫县名称</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">主导产业</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">产业帮扶投入(万元)</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">脱贫人口增收(元/人)</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">帮扶成效评级</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">返贫风险</th>
            </tr>
          </thead>
          <tbody id="poverty-table-body">
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 text-text-primary">奉节县</td>
              <td class="border border-border px-4 py-3 text-text-primary">脐橙种植加工</td>
              <td class="border border-border px-4 py-3 text-text-primary">2,850</td>
              <td class="border border-border px-4 py-3 text-primary font-semibold">8,420</td>
              <td class="border border-border px-4 py-3"><span class="px-2 py-1 bg-primary/10 text-primary rounded-full text-sm">优秀</span></td>
              <td class="border border-border px-4 py-3"><span class="px-2 py-1 bg-green-100 text-green-700 rounded-full text-sm">低风险</span></td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 text-text-primary">巫溪县</td>
              <td class="border border-border px-4 py-3 text-text-primary">中药材种植</td>
              <td class="border border-border px-4 py-3 text-text-primary">1,920</td>
              <td class="border border-border px-4 py-3 text-primary font-semibold">6,780</td>
              <td class="border border-border px-4 py-3"><span class="px-2 py-1 bg-secondary/10 text-secondary rounded-full text-sm">良好</span></td>
              <td class="border border-border px-4 py-3"><span class="px-2 py-1 bg-green-100 text-green-700 rounded-full text-sm">低风险</span></td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 text-text-primary">城口县</td>
              <td class="border border-border px-4 py-3 text-text-primary">中蜂养殖</td>
              <td class="border border-border px-4 py-3 text-text-primary">1,450</td>
              <td class="border border-border px-4 py-3 text-primary font-semibold">5,960</td>
              <td class="border border-border px-4 py-3"><span class="px-2 py-1 bg-secondary/10 text-secondary rounded-full text-sm">良好</span></td>
              <td class="border border-border px-4 py-3"><span class="px-2 py-1 bg-yellow-100 text-yellow-700 rounded-full text-sm">中风险</span></td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 text-text-primary">酉阳县</td>
              <td class="border border-border px-4 py-3 text-text-primary">茶叶种植</td>
              <td class="border border-border px-4 py-3 text-text-primary">2,180</td>
              <td class="border border-border px-4 py-3 text-primary font-semibold">7,320</td>
              <td class="border border-border px-4 py-3"><span class="px-2 py-1 bg-primary/10 text-primary rounded-full text-sm">优秀</span></td>
              <td class="border border-border px-4 py-3"><span class="px-2 py-1 bg-green-100 text-green-700 rounded-full text-sm">低风险</span></td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 text-text-primary">秀山县</td>
              <td class="border border-border px-4 py-3 text-text-primary">土家织锦</td>
              <td class="border border-border px-4 py-3 text-text-primary">980</td>
              <td class="border border-border px-4 py-3 text-primary font-semibold">4,650</td>
              <td class="border border-border px-4 py-3"><span class="px-2 py-1 bg-accent/10 text-accent rounded-full text-sm">一般</span></td>
              <td class="border border-border px-4 py-3"><span class="px-2 py-1 bg-yellow-100 text-yellow-700 rounded-full text-sm">中风险</span></td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <!-- 分页信息 -->
      <div class="flex justify-between items-center mt-4 pt-4 border-t border-border">
        <div class="text-text-secondary">
          共 5 条，每页显示 5 条，第 1 页/共 1 页
        </div>
        <div class="flex space-x-2">
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary cursor-not-allowed">
            上一页
          </button>
          <button class="px-3 py-1 bg-primary text-white border border-primary rounded">
            1
          </button>
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary cursor-not-allowed">
            下一页
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 页脚 -->
  <footer class="bg-lighter border-t border-border mt-12 py-6">
    <div class="container mx-auto px-6 text-center">
      <p class="text-text-secondary">© 2025 保留所有权利.</p>
    </div>
  </footer>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // 设置当前日期为默认值
      const today = new Date();
      const formattedDate = today.toISOString().split('T')[0];
      const lastYear = new Date(today.getFullYear() - 1, today.getMonth(), today.getDate());
      const lastYearFormatted = lastYear.toISOString().split('T')[0];

      document.getElementById('start-date').value = lastYearFormatted;
      document.getElementById('end-date').value = formattedDate;

      // 脱贫县人均用电量对比柱状图
      const comparisonCtx = document.getElementById('power-comparison-chart').getContext('2d');
      const powerComparisonChart = new Chart(comparisonCtx, {
        type: 'bar',
        data: {
          labels: ['奉节县', '巫溪县', '城口县', '酉阳县', '秀山县', '彭水县', '石柱县', '武隆区'],
          datasets: [
            {
              label: '2024年人均用电量(kWh)',
              data: [2850, 2420, 1980, 2650, 1850, 2180, 2320, 2090],
              backgroundColor: '#31969A',
              borderColor: '#31969A',
              borderWidth: 1,
              borderRadius: 4
            },
            {
              label: '2023年人均用电量(kWh)',
              data: [2650, 2280, 1820, 2480, 1720, 2050, 2180, 1950],
              backgroundColor: '#80CCE3',
              borderColor: '#80CCE3',
              borderWidth: 1,
              borderRadius: 4
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'top',
              labels: {
                padding: 20,
                usePointStyle: true,
                font: {
                  size: 12
                }
              }
            },
            tooltip: {
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              titleColor: '#1E293B',
              bodyColor: '#64748B',
              borderColor: 'rgba(49, 150, 154, 0.2)',
              borderWidth: 1,
              padding: 12,
              callbacks: {
                label: function(context) {
                  return context.dataset.label + ': ' + context.parsed.y + ' kWh';
                }
              }
            }
          },
          scales: {
            x: {
              grid: {
                display: false
              },
              ticks: {
                color: '#64748B',
                font: {
                  size: 11
                },
                maxRotation: 45
              }
            },
            y: {
              beginAtZero: true,
              grid: {
                color: 'rgba(226, 232, 240, 0.7)',
                width: 1
              },
              ticks: {
                color: '#64748B',
                font: {
                  size: 12
                },
                callback: function(value) {
                  return value + ' kWh';
                }
              }
            }
          }
        }
      });

      // 查询按钮事件
      document.getElementById('btn-search').addEventListener('click', function () {
        const managementUnit = document.getElementById('management-unit').value;
        const startDate = document.getElementById('start-date').value;
        const endDate = document.getElementById('end-date').value;

        console.log(`查询条件 - 管理单位: ${managementUnit}, 开始日期: ${startDate}, 结束日期: ${endDate}`);
        updateChartAndTable(managementUnit, startDate, endDate);
      });

      // 下载报告按钮事件
      document.getElementById('btn-download-report').addEventListener('click', function () {
        console.log('下载成效分析报告');
        // 模拟下载报告
        const link = document.createElement('a');
        link.href = '#';
        link.download = '脱贫县产业扶贫成效评估报告_' + new Date().toISOString().split('T')[0] + '.pdf';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        alert('成效分析报告下载中...');
      });

      // 更新图表和表格数据的函数
      function updateChartAndTable(unit, startDate, endDate) {
        // 模拟根据查询条件更新数据
        if (unit !== 'all') {
          // 根据选择的单位过滤数据
          const unitData = getDataByUnit(unit);
          powerComparisonChart.data.labels = unitData.labels;
          powerComparisonChart.data.datasets[0].data = unitData.currentYear;
          powerComparisonChart.data.datasets[1].data = unitData.lastYear;
          powerComparisonChart.update();
        } else {
          // 恢复全部数据
          powerComparisonChart.data.labels = ['奉节县', '巫溪县', '城口县', '酉阳县', '秀山县', '彭水县', '石柱县', '武隆区'];
          powerComparisonChart.data.datasets[0].data = [2850, 2420, 1980, 2650, 1850, 2180, 2320, 2090];
          powerComparisonChart.data.datasets[1].data = [2650, 2280, 1820, 2480, 1720, 2050, 2180, 1950];
          powerComparisonChart.update();
        }
      }

      // 根据单位获取数据的辅助函数
      function getDataByUnit(unit) {
        const unitDataMap = {
          'unit1': {
            labels: ['奉节县'],
            currentYear: [2850],
            lastYear: [2650]
          },
          'unit2': {
            labels: ['巫溪县'],
            currentYear: [2420],
            lastYear: [2280]
          },
          'unit3': {
            labels: ['城口县'],
            currentYear: [1980],
            lastYear: [1820]
          },
          'unit4': {
            labels: ['酉阳县'],
            currentYear: [2650],
            lastYear: [2480]
          },
          'unit5': {
            labels: ['秀山县'],
            currentYear: [1850],
            lastYear: [1720]
          }
        };

        return unitDataMap[unit] || {
          labels: ['奉节县', '巫溪县', '城口县', '酉阳县', '秀山县', '彭水县', '石柱县', '武隆区'],
          currentYear: [2850, 2420, 1980, 2650, 1850, 2180, 2320, 2090],
          lastYear: [2650, 2280, 1820, 2480, 1720, 2050, 2180, 1950]
        };
      }

      // 滚动效果
      window.addEventListener('scroll', function () {
        const header = document.querySelector('header');
        if (window.scrollY > 10) {
          header.classList.add('shadow-md');
        } else {
          header.classList.remove('shadow-md');
        }
      });
    });
  </script>
</body>

</html>
