<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>特色优势产业管理</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#31969A',
            secondary: '#80CCE3',
            accent: '#8199C7',
            warning: '#E5CE66',
            danger: '#E74C3C',
            light: '#F8FAFC',
            lighter: '#FFFFFF',
            border: '#E2E8F0',
            grid: 'rgba(226, 232, 240, 0.5)',
            text: {
              primary: '#1E293B',
              secondary: '#64748B',
              tertiary: '#94A3B8'
            }
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
        },
      }
    }
  </script>
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .card-shadow {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
      }
      .chart-container {
        position: relative;
        height: 300px;
        width: 100%;
      }
      .scrollbar-thin {
        scrollbar-width: thin;
      }
      .scrollbar-thin::-webkit-scrollbar {
        width: 6px;
      }
      .scrollbar-thin::-webkit-scrollbar-thumb {
        background-color: rgba(100, 116, 139, 0.3);
        border-radius: 3px;
      }
      .stat-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(49, 150, 154, 0.1);
      }
    }
  </style>
</head>

<body class="bg-light font-inter text-text-primary min-h-screen">
  <!-- 顶部导航栏 -->
  <header class="bg-lighter/90 backdrop-blur-md border-b border-border sticky top-0 z-50 transition-all duration-300">
    <div class="container mx-auto px-6 py-3 flex justify-between items-center">
      <div class="flex items-center space-x-3">
        <i class="fa fa-industry text-primary text-3xl"></i>
      </div>
      <div class="flex items-center space-x-4">
        <div class="bg-light/70 border border-border rounded-lg px-4 py-2 flex items-center">
          <i class="fa fa-calendar-o mr-2 text-text-secondary"></i>
          <span id="current-date-display" class="text-text-primary">2025年8月5日</span>
        </div>
      </div>
    </div>
  </header>

  <div class="container mx-auto px-6 py-6">
    <!-- 控制面板 -->
    <div class="bg-lighter border border-border rounded-xl p-6 mb-6 card-shadow">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">管理单位</label>
          <div class="relative">
            <select id="management-unit" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="all">全部单位</option>
              <option value="unit1">奉节县农业农村委</option>
              <option value="unit2">江津区农业农村委</option>
              <option value="unit3">巫山县农业农村委</option>
              <option value="unit4">石柱县农业农村委</option>
              <option value="unit5">城口县农业农村委</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">日期范围</label>
          <div class="flex space-x-3">
            <input type="date" id="start-date" class="flex-1 bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
            <input type="date" id="end-date" class="flex-1 bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">操作</label>
          <div class="flex space-x-3">
            <button id="btn-search" class="flex-1 px-4 py-3 bg-primary text-white rounded-lg border border-primary hover:bg-primary/90 transition-colors">
              <i class="fa fa-search mr-1"></i> 查询
            </button>
            <button id="btn-export" class="px-4 py-3 bg-primary/20 text-primary rounded-lg border border-primary/30 hover:bg-primary/30 transition-colors">
              <i class="fa fa-download mr-1"></i> 导出
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
      <!-- 特色优势产业类型分布饼状图 -->
      <div class="bg-lighter border border-border rounded-xl p-6 card-shadow">
        <h3 class="text-xl font-semibold text-text-primary mb-4">特色优势产业类型分布</h3>
        <div class="h-[300px] chart-container">
          <canvas id="industry-pie-chart"></canvas>
        </div>
      </div>

      <!-- 重点区县特色产业数量柱状图 -->
      <div class="bg-lighter border border-border rounded-xl p-6 card-shadow">
        <h3 class="text-xl font-semibold text-text-primary mb-4">重点区县特色产业数量</h3>
        <div class="h-[300px] chart-container">
          <canvas id="county-bar-chart"></canvas>
        </div>
      </div>
    </div>

    <!-- 特色优势产业明细表格 -->
    <div class="bg-lighter border border-border rounded-xl p-6 card-shadow">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold text-text-primary">重点监测县特色优势产业明细</h3>
        <div class="flex space-x-3">
          <button id="btn-view-detail" class="px-4 py-2 bg-secondary/20 text-primary rounded-lg border border-secondary/30 hover:bg-secondary/30 transition-colors">
            <i class="fa fa-eye mr-1"></i> 查看明细
          </button>
        </div>
      </div>
      
      <div class="overflow-x-auto">
        <table class="w-full border-collapse border border-border">
          <thead>
            <tr class="bg-light">
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">管理单位</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">特色产业名称</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">近三个月用电量(万kWh)</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">用电增长率(%)</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">产业规模</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">就业人数(人)</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">操作</th>
            </tr>
          </thead>
          <tbody id="industry-table-body">
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 text-text-primary">奉节县农业农村委</td>
              <td class="border border-border px-4 py-3 text-text-primary">脐橙种植加工</td>
              <td class="border border-border px-4 py-3 text-text-primary">186.4</td>
              <td class="border border-border px-4 py-3 text-primary font-semibold">+14.2</td>
              <td class="border border-border px-4 py-3 text-text-primary">大型</td>
              <td class="border border-border px-4 py-3 text-text-primary">4,280</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2">
                  <i class="fa fa-eye"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 text-text-primary">江津区农业农村委</td>
              <td class="border border-border px-4 py-3 text-text-primary">花椒种植加工</td>
              <td class="border border-border px-4 py-3 text-text-primary">92.7</td>
              <td class="border border-border px-4 py-3 text-primary font-semibold">+9.8</td>
              <td class="border border-border px-4 py-3 text-text-primary">中型</td>
              <td class="border border-border px-4 py-3 text-text-primary">2,150</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2">
                  <i class="fa fa-eye"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 text-text-primary">巫山县农业农村委</td>
              <td class="border border-border px-4 py-3 text-text-primary">脆李种植</td>
              <td class="border border-border px-4 py-3 text-text-primary">78.5</td>
              <td class="border border-border px-4 py-3 text-primary font-semibold">+16.7</td>
              <td class="border border-border px-4 py-3 text-text-primary">中型</td>
              <td class="border border-border px-4 py-3 text-text-primary">1,890</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2">
                  <i class="fa fa-eye"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 text-text-primary">石柱县农业农村委</td>
              <td class="border border-border px-4 py-3 text-text-primary">莼菜种植</td>
              <td class="border border-border px-4 py-3 text-text-primary">45.3</td>
              <td class="border border-border px-4 py-3 text-primary font-semibold">+22.1</td>
              <td class="border border-border px-4 py-3 text-text-primary">小型</td>
              <td class="border border-border px-4 py-3 text-text-primary">980</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2">
                  <i class="fa fa-eye"></i>
                </button>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3 text-text-primary">城口县农业农村委</td>
              <td class="border border-border px-4 py-3 text-text-primary">中蜂养殖</td>
              <td class="border border-border px-4 py-3 text-text-primary">32.8</td>
              <td class="border border-border px-4 py-3 text-primary font-semibold">+11.4</td>
              <td class="border border-border px-4 py-3 text-text-primary">小型</td>
              <td class="border border-border px-4 py-3 text-text-primary">760</td>
              <td class="border border-border px-4 py-3">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2">
                  <i class="fa fa-eye"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <!-- 分页信息 -->
      <div class="flex justify-between items-center mt-4 pt-4 border-t border-border">
        <div class="text-text-secondary">
          共 5 条，每页显示 5 条，第 1 页/共 1 页
        </div>
        <div class="flex space-x-2">
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary cursor-not-allowed">
            上一页
          </button>
          <button class="px-3 py-1 bg-primary text-white border border-primary rounded">
            1
          </button>
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary cursor-not-allowed">
            下一页
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 页脚 -->
  <footer class="bg-lighter border-t border-border mt-12 py-6">
    <div class="container mx-auto px-6 text-center">
      <p class="text-text-secondary">© 2025 保留所有权利.</p>
    </div>
  </footer>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // 设置当前日期为默认值
      const today = new Date();
      const formattedDate = today.toISOString().split('T')[0];
      const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
      const lastMonthFormatted = lastMonth.toISOString().split('T')[0];

      document.getElementById('start-date').value = lastMonthFormatted;
      document.getElementById('end-date').value = formattedDate;

      // 特色优势产业类型分布饼状图
      const pieCtx = document.getElementById('industry-pie-chart').getContext('2d');
      const industryPieChart = new Chart(pieCtx, {
        type: 'pie',
        data: {
          labels: ['脐橙种植加工', '花椒种植加工', '脆李种植', '莼菜种植', '中蜂养殖'],
          datasets: [{
            data: [32, 24, 18, 15, 11],
            backgroundColor: [
              '#31969A',
              '#80CCE3',
              '#8199C7',
              '#E5CE66',
              '#F87272'
            ],
            borderWidth: 2,
            borderColor: '#FFFFFF'
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom',
              labels: {
                padding: 20,
                usePointStyle: true,
                font: {
                  size: 12
                }
              }
            },
            tooltip: {
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              titleColor: '#1E293B',
              bodyColor: '#64748B',
              borderColor: 'rgba(49, 150, 154, 0.2)',
              borderWidth: 1,
              padding: 12,
              callbacks: {
                label: function(context) {
                  return context.label + ': ' + context.parsed + '%';
                }
              }
            }
          }
        }
      });

      // 重点区县特色产业数量柱状图
      const barCtx = document.getElementById('county-bar-chart').getContext('2d');
      const countyBarChart = new Chart(barCtx, {
        type: 'bar',
        data: {
          labels: ['奉节县', '江津区', '巫山县', '石柱县', '城口县'],
          datasets: [{
            label: '特色产业数量',
            data: [12, 8, 7, 5, 3],
            backgroundColor: '#31969A',
            borderColor: '#31969A',
            borderWidth: 1,
            borderRadius: 4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            },
            tooltip: {
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              titleColor: '#1E293B',
              bodyColor: '#64748B',
              borderColor: 'rgba(49, 150, 154, 0.2)',
              borderWidth: 1,
              padding: 12,
              callbacks: {
                label: function(context) {
                  return '产业数量: ' + context.parsed.y + ' 个';
                }
              }
            }
          },
          scales: {
            x: {
              grid: {
                display: false
              },
              ticks: {
                color: '#64748B',
                font: {
                  size: 12
                }
              }
            },
            y: {
              beginAtZero: true,
              grid: {
                color: 'rgba(226, 232, 240, 0.7)',
                width: 1
              },
              ticks: {
                color: '#64748B',
                font: {
                  size: 12
                },
                callback: function(value) {
                  return value + ' 个';
                }
              }
            }
          }
        }
      });

      // 查询按钮事件
      document.getElementById('btn-search').addEventListener('click', function () {
        const managementUnit = document.getElementById('management-unit').value;
        const startDate = document.getElementById('start-date').value;
        const endDate = document.getElementById('end-date').value;

        console.log(`查询条件 - 管理单位: ${managementUnit}, 开始日期: ${startDate}, 结束日期: ${endDate}`);
        // 这里可以添加实际的查询逻辑
        updateChartsAndTable(managementUnit, startDate, endDate);
      });

      // 导出按钮事件
      document.getElementById('btn-export').addEventListener('click', function () {
        console.log('导出特色优势产业数据');
        // 这里可以添加导出数据的逻辑
        alert('数据导出功能开发中...');
      });

      // 查看明细按钮事件
      document.getElementById('btn-view-detail').addEventListener('click', function () {
        console.log('查看产业用电明细');
        // 这里可以添加查看明细的逻辑
        alert('产业用电明细功能开发中...');
      });

      // 表格中的查看按钮事件
      document.querySelectorAll('#industry-table-body button').forEach(button => {
        button.addEventListener('click', function () {
          const row = this.closest('tr');
          const industryName = row.cells[1].textContent;
          console.log(`查看 ${industryName} 的详细信息`);
          alert(`查看 ${industryName} 的详细信息功能开发中...`);
        });
      });

      // 更新图表和表格数据的函数
      function updateChartsAndTable(unit, startDate, endDate) {
        // 模拟根据查询条件更新数据
        // 实际应用中这里会调用API获取数据

        // 更新饼状图数据
        if (unit !== 'all') {
          // 根据选择的单位过滤数据
          const unitData = getDataByUnit(unit);
          industryPieChart.data.labels = unitData.labels;
          industryPieChart.data.datasets[0].data = unitData.data;
          industryPieChart.update();
        }

        // 更新柱状图数据
        const randomVariation = () => Math.floor(Math.random() * 5) + 3; // 3-7之间的随机数
        countyBarChart.data.datasets[0].data = countyBarChart.data.datasets[0].data.map(() => randomVariation());
        countyBarChart.update();
      }

      // 根据单位获取数据的辅助函数
      function getDataByUnit(unit) {
        const unitDataMap = {
          'unit1': {
            labels: ['脐橙种植加工', '柠檬种植', '榨菜种植'],
            data: [55, 30, 15]
          },
          'unit2': {
            labels: ['花椒种植加工', '脐橙种植', '茶叶种植'],
            data: [45, 35, 20]
          },
          'unit3': {
            labels: ['脆李种植', '猕猴桃种植', '茶叶种植'],
            data: [50, 30, 20]
          },
          'unit4': {
            labels: ['莼菜种植', '辣椒种植', '土豆种植'],
            data: [60, 25, 15]
          },
          'unit5': {
            labels: ['中蜂养殖', '中药材种植', '山羊养殖'],
            data: [50, 30, 20]
          }
        };

        return unitDataMap[unit] || {
          labels: ['脐橙种植加工', '花椒种植加工', '脆李种植', '莼菜种植', '中蜂养殖'],
          data: [32, 24, 18, 15, 11]
        };
      }

      // 滚动效果
      window.addEventListener('scroll', function () {
        const header = document.querySelector('header');
        if (window.scrollY > 10) {
          header.classList.add('shadow-md');
        } else {
          header.classList.remove('shadow-md');
        }
      });
    });
  </script>
</body>

</html>
