<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>******* 政策挖掘 - 大模型能源电力政策信息挖掘系统</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#31969A',
            secondary: '#80CCE3',
            accent: '#8199C7',
            warning: '#E5CE66',
            danger: '#E74C3C',
            success: '#10B981',
            light: '#F8FAFC',
            lighter: '#FFFFFF',
            border: '#E2E8F0',
            grid: 'rgba(226, 232, 240, 0.5)',
            text: {
              primary: '#1E293B',
              secondary: '#64748B',
              tertiary: '#94A3B8'
            }
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
        },
      }
    }
  </script>
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .card-shadow {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
      }
      .scrollbar-thin {
        scrollbar-width: thin;
      }
      .scrollbar-thin::-webkit-scrollbar {
        width: 6px;
      }
      .scrollbar-thin::-webkit-scrollbar-thumb {
        background-color: rgba(100, 116, 139, 0.3);
        border-radius: 3px;
      }
      .stat-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(49, 150, 154, 0.1);
      }
      .upload-area {
        border: 2px dashed #E2E8F0;
        transition: all 0.3s ease;
      }
      .upload-area:hover {
        border-color: #31969A;
        background-color: rgba(49, 150, 154, 0.02);
      }
      .upload-area.dragover {
        border-color: #31969A;
        background-color: rgba(49, 150, 154, 0.05);
      }
    }
  </style>
</head>

<body class="bg-light font-inter text-text-primary min-h-screen">
  <!-- 顶部导航栏 -->
  <header class="bg-lighter/90 backdrop-blur-md border-b border-border sticky top-0 z-50 transition-all duration-300">
    <div class="container mx-auto px-6 py-3 flex justify-between items-center">
      <div class="flex items-center space-x-3">
        <i class="fa fa-database text-primary text-3xl"></i>
        <div>
          <h1 class="text-lg font-semibold text-text-primary">******* 政策挖掘</h1>
          <p class="text-sm text-text-secondary">能源电力及乡村振兴政策文件管理系统</p>
        </div>
      </div>
      <div class="flex items-center space-x-4">
        <div class="bg-light/70 border border-border rounded-lg px-4 py-2 flex items-center">
          <i class="fa fa-calendar-o mr-2 text-text-secondary"></i>
          <span id="current-date-display" class="text-text-primary">2025年8月4日</span>
        </div>
      </div>
    </div>
  </header>

  <div class="container mx-auto px-6 py-6">
    <!-- 政策文件统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">政策文件总数</p>
            <h3 class="text-3xl font-bold mt-2">2,853 <span class="text-lg font-normal text-text-secondary">份</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center text-primary">
            <i class="fa fa-file-text text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-success flex items-center">
            <i class="fa fa-arrow-up mr-1"></i> 18.5%
          </span>
          <span class="text-text-secondary ml-2">较上月</span>
        </div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">本月新增</p>
            <h3 class="text-3xl font-bold mt-2">156 <span class="text-lg font-normal text-text-secondary">份</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-success/10 flex items-center justify-center text-success">
            <i class="fa fa-plus text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-success flex items-center">
            <i class="fa fa-arrow-up mr-1"></i> 12.3%
          </span>
          <span class="text-text-secondary ml-2">较上月</span>
        </div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">国家级政策</p>
            <h3 class="text-3xl font-bold mt-2">434 <span class="text-lg font-normal text-text-secondary">份</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-accent/10 flex items-center justify-center text-accent">
            <i class="fa fa-star text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-success flex items-center">
            <i class="fa fa-arrow-up mr-1"></i> 8.7%
          </span>
          <span class="text-text-secondary ml-2">较上月</span>
        </div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">关键词数量</p>
            <h3 class="text-3xl font-bold mt-2">5,672 <span class="text-lg font-normal text-text-secondary">个</span></h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-secondary/10 flex items-center justify-center text-secondary">
            <i class="fa fa-tags text-xl"></i>
          </div>
        </div>
        <div class="flex items-center mt-3">
          <span class="text-success flex items-center">
            <i class="fa fa-arrow-up mr-1"></i> 25.1%
          </span>
          <span class="text-text-secondary ml-2">较上月</span>
        </div>
      </div>
    </div>

    <!-- 查询条件面板 -->
    <div class="bg-lighter border border-border rounded-xl p-6 mb-6 card-shadow">
      <h2 class="text-xl font-semibold text-text-primary mb-4">政策文件查询</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">政策标题</label>
          <input type="text" id="policy-title" placeholder="请输入政策标题关键词" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">发文单位</label>
          <input type="text" id="issuing-unit" placeholder="请输入发文单位" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">发文号</label>
          <input type="text" id="document-number" placeholder="请输入发文号" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">政策层级</label>
          <div class="relative">
            <select id="policy-level" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors appearance-none">
              <option value="">全部层级</option>
              <option value="国家级">国家级</option>
              <option value="省级">省级</option>
              <option value="市级">市级</option>
              <option value="县级">县级</option>
            </select>
            <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
              <i class="fa fa-chevron-down text-text-secondary"></i>
            </div>
          </div>
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">发布日期</label>
          <input type="date" id="publish-date" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
        </div>

        <div class="flex flex-col">
          <label class="text-text-secondary mb-2">关键词</label>
          <input type="text" id="keywords" placeholder="请输入关键词" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
        </div>
      </div>

      <div class="flex justify-between items-center">
        <div class="flex space-x-3">
          <button id="btn-search" class="px-4 py-3 bg-primary text-white rounded-lg border border-primary hover:bg-primary/90 transition-colors">
            <i class="fa fa-search mr-1"></i> 查询
          </button>
          <button id="btn-reset" class="px-4 py-3 bg-light text-text-secondary rounded-lg border border-border hover:bg-light/70 transition-colors">
            <i class="fa fa-refresh mr-1"></i> 重置
          </button>
        </div>
        <button id="btn-upload" class="px-4 py-3 bg-success text-white rounded-lg border border-success hover:bg-success/90 transition-colors">
          <i class="fa fa-upload mr-1"></i> 上传政策文件
        </button>
      </div>
    </div>

    <!-- 政策文件列表 -->
    <div class="bg-lighter border border-border rounded-xl p-6 card-shadow mb-6">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold text-text-primary">政策文件列表</h3>
        <div class="flex space-x-3">
          <button class="px-4 py-2 bg-secondary/20 text-secondary rounded-lg border border-secondary/30 hover:bg-secondary/30 transition-colors">
            <i class="fa fa-download mr-1"></i> 批量导出
          </button>
        </div>
      </div>

      <div class="overflow-x-auto scrollbar-thin">
        <table class="w-full min-w-[1400px] border border-border rounded-lg">
          <thead class="bg-light border-b border-border">
            <tr>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border">政策标题</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border">发文单位</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border">发文号</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border">发布日期</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border">政策层级</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary border-r border-border">录入时间</th>
              <th class="px-4 py-3 text-left font-semibold text-text-primary">操作</th>
            </tr>
          </thead>
          <tbody id="policy-table-body">
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-3 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-3 text-text-primary border-r border-border">
                <div class="font-medium">关于实现巩固拓展脱贫攻坚成果同乡村振兴有效衔接的意见</div>
                <div class="text-sm text-text-secondary mt-1">乡村振兴、脱贫攻坚、有效衔接</div>
              </td>
              <td class="px-4 py-3 text-text-primary border-r border-border">中共中央 国务院</td>
              <td class="px-4 py-3 text-text-primary border-r border-border">中发〔2020〕30号</td>
              <td class="px-4 py-3 text-text-primary border-r border-border">2020-12-16</td>
              <td class="px-4 py-3 border-r border-border"><span class="px-2 py-1 bg-danger/10 text-danger rounded-full text-sm">国家级</span></td>
              <td class="px-4 py-3 text-text-primary border-r border-border">2024-08-01 09:30</td>
              <td class="px-4 py-3">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="预览">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-secondary hover:text-secondary/80 transition-colors mr-2" title="下载">
                  <i class="fa fa-download"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/80 transition-colors" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-3 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-3 text-text-primary border-r border-border">
                <div class="font-medium">关于促进新时代新能源高质量发展的实施方案</div>
                <div class="text-sm text-text-secondary mt-1">新能源、高质量发展、实施方案</div>
              </td>
              <td class="px-4 py-3 text-text-primary border-r border-border">国务院办公厅</td>
              <td class="px-4 py-3 text-text-primary border-r border-border">国办发〔2022〕39号</td>
              <td class="px-4 py-3 text-text-primary border-r border-border">2022-05-30</td>
              <td class="px-4 py-3 border-r border-border"><span class="px-2 py-1 bg-danger/10 text-danger rounded-full text-sm">国家级</span></td>
              <td class="px-4 py-3 text-text-primary border-r border-border">2024-07-28 14:20</td>
              <td class="px-4 py-3">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="预览">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-secondary hover:text-secondary/80 transition-colors mr-2" title="下载">
                  <i class="fa fa-download"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/80 transition-colors" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-3 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-3 text-text-primary border-r border-border">
                <div class="font-medium">中共中央国务院关于进一步深化农村改革扎实推进乡村全面振兴的意见</div>
                <div class="text-sm text-text-secondary mt-1">农村改革、乡村振兴、持续巩固拓展脱贫攻坚成果</div>
              </td>
              <td class="px-4 py-3 text-text-primary border-r border-border">中共中央 国务院</td>
              <td class="px-4 py-3 text-text-primary border-r border-border">中发〔2025〕1号</td>
              <td class="px-4 py-3 text-text-primary border-r border-border">2025-02-23</td>
              <td class="px-4 py-3 border-r border-border"><span class="px-2 py-1 bg-danger/10 text-danger rounded-full text-sm">国家级</span></td>
              <td class="px-4 py-3 text-text-primary border-r border-border">2025-02-24 08:00</td>
              <td class="px-4 py-3">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="预览">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-secondary hover:text-secondary/80 transition-colors mr-2" title="下载">
                  <i class="fa fa-download"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/80 transition-colors" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-3 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-3 text-text-primary border-r border-border">
                <div class="font-medium">关于实施农村电网巩固提升工程的指导意见</div>
                <div class="text-sm text-text-secondary mt-1">农村电网、巩固提升、脱贫攻坚、乡村振兴</div>
              </td>
              <td class="px-4 py-3 text-text-primary border-r border-border">
                <div class="text-sm">国家发展改革委</div>
                <div class="text-sm">国家能源局</div>
                <div class="text-sm">国家乡村振兴局</div>
              </td>
              <td class="px-4 py-3 text-text-primary border-r border-border">发改能源〔2023〕1086号</td>
              <td class="px-4 py-3 text-text-primary border-r border-border">2023-07-04</td>
              <td class="px-4 py-3 border-r border-border"><span class="px-2 py-1 bg-danger/10 text-danger rounded-full text-sm">国家级</span></td>
              <td class="px-4 py-3 text-text-primary border-r border-border">2023-07-05 10:15</td>
              <td class="px-4 py-3">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="预览">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-secondary hover:text-secondary/80 transition-colors mr-2" title="下载">
                  <i class="fa fa-download"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/80 transition-colors" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-3 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-3 text-text-primary border-r border-border">
                <div class="font-medium">"十四五"可再生能源发展规划</div>
                <div class="text-sm text-text-secondary mt-1">可再生能源、农村电网、脱贫地区、乡村振兴</div>
              </td>
              <td class="px-4 py-3 text-text-primary border-r border-border">
                <div class="text-sm">国家发展改革委</div>
                <div class="text-sm">国家能源局</div>
              </td>
              <td class="px-4 py-3 text-text-primary border-r border-border">发改能源〔2022〕1102号</td>
              <td class="px-4 py-3 text-text-primary border-r border-border">2022-06-01</td>
              <td class="px-4 py-3 border-r border-border"><span class="px-2 py-1 bg-danger/10 text-danger rounded-full text-sm">国家级</span></td>
              <td class="px-4 py-3 text-text-primary border-r border-border">2022-06-02 16:30</td>
              <td class="px-4 py-3">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="预览">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-secondary hover:text-secondary/80 transition-colors mr-2" title="下载">
                  <i class="fa fa-download"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/80 transition-colors" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-3 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-3 text-text-primary border-r border-border">
                <div class="font-medium">重庆市巩固拓展脱贫攻坚成果同乡村振兴有效衔接实施方案</div>
                <div class="text-sm text-text-secondary mt-1">重庆、脱贫攻坚、乡村振兴、有效衔接、防返贫监测</div>
              </td>
              <td class="px-4 py-3 text-text-primary border-r border-border">重庆市人民政府</td>
              <td class="px-4 py-3 text-text-primary border-r border-border">渝府发〔2021〕45号</td>
              <td class="px-4 py-3 text-text-primary border-r border-border">2021-12-15</td>
              <td class="px-4 py-3 border-r border-border"><span class="px-2 py-1 bg-warning/10 text-warning rounded-full text-sm">市级</span></td>
              <td class="px-4 py-3 text-text-primary border-r border-border">2021-12-16 09:45</td>
              <td class="px-4 py-3">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="预览">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-secondary hover:text-secondary/80 transition-colors mr-2" title="下载">
                  <i class="fa fa-download"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/80 transition-colors" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr class="border-b border-border hover:bg-light/50 transition-colors">
              <td class="px-4 py-3 border-r border-border">
                <input type="checkbox" class="rounded border-border">
              </td>
              <td class="px-4 py-3 text-text-primary border-r border-border">
                <div class="font-medium">关于加强电力数据在乡村振兴中应用的指导意见</div>
                <div class="text-sm text-text-secondary mt-1">电力数据、乡村振兴、用能分析、产业振兴评估</div>
              </td>
              <td class="px-4 py-3 text-text-primary border-r border-border">
                <div class="text-sm">国家能源局</div>
                <div class="text-sm">国家乡村振兴局</div>
              </td>
              <td class="px-4 py-3 text-text-primary border-r border-border">国能发电力〔2024〕28号</td>
              <td class="px-4 py-3 text-text-primary border-r border-border">2024-03-15</td>
              <td class="px-4 py-3 border-r border-border"><span class="px-2 py-1 bg-danger/10 text-danger rounded-full text-sm">国家级</span></td>
              <td class="px-4 py-3 text-text-primary border-r border-border">2024-03-16 11:20</td>
              <td class="px-4 py-3">
                <button class="text-primary hover:text-primary/80 transition-colors mr-2" title="预览">
                  <i class="fa fa-eye"></i>
                </button>
                <button class="text-secondary hover:text-secondary/80 transition-colors mr-2" title="下载">
                  <i class="fa fa-download"></i>
                </button>
                <button class="text-accent hover:text-accent/80 transition-colors mr-2" title="编辑">
                  <i class="fa fa-edit"></i>
                </button>
                <button class="text-danger hover:text-danger/80 transition-colors" title="删除">
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页信息 -->
      <div class="flex justify-between items-center mt-4 pt-4 border-t border-border">
        <div class="text-text-secondary">
          共 7 条，每页显示 10 条，第 1 页/共 1 页
        </div>
        <div class="flex space-x-2">
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary hover:bg-light/70 transition-colors disabled:opacity-50" disabled>
            <i class="fa fa-chevron-left"></i> 上一页
          </button>
          <button class="px-3 py-1 bg-primary text-white border border-primary rounded">1</button>
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary hover:bg-light/70 transition-colors">2</button>
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary hover:bg-light/70 transition-colors">3</button>
          <button class="px-3 py-1 bg-light border border-border rounded text-text-secondary hover:bg-light/70 transition-colors">
            下一页 <i class="fa fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 上传政策文件模态框 -->
  <div id="upload-modal" class="fixed inset-0 bg-black/10 flex items-center justify-center z-50 hidden">
    <div class="bg-lighter border border-border rounded-xl p-8 w-full max-w-2xl transform transition-all duration-300 scale-95 opacity-0" id="upload-modal-content">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-2xl font-semibold text-text-primary">上传政策文件</h3>
        <button id="close-upload-modal" class="text-text-secondary hover:text-primary transition-colors">
          <i class="fa fa-times text-xl"></i>
        </button>
      </div>

      <form class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="text-text-secondary block mb-2">政策标题 *</label>
            <input type="text" id="upload-title" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors" placeholder="请输入政策标题">
          </div>
          <div>
            <label class="text-text-secondary block mb-2">发文单位 *</label>
            <input type="text" id="upload-unit" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors" placeholder="请输入发文单位">
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="text-text-secondary block mb-2">发文号</label>
            <input type="text" id="upload-number" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors" placeholder="请输入发文号">
          </div>
          <div>
            <label class="text-text-secondary block mb-2">政策层级 *</label>
            <select id="upload-level" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
              <option value="">请选择政策层级</option>
              <option value="国家级">国家级</option>
              <option value="省级">省级</option>
              <option value="市级">市级</option>
              <option value="县级">县级</option>
            </select>
          </div>
        </div>

        <div>
          <label class="text-text-secondary block mb-2">发布日期 *</label>
          <input type="date" id="upload-date" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors">
        </div>

        <div>
          <label class="text-text-secondary block mb-2">关键词</label>
          <input type="text" id="upload-keywords" class="w-full bg-light border border-border rounded-lg px-4 py-3 text-text-primary outline-none focus:border-primary transition-colors" placeholder="请输入关键词，多个关键词用逗号分隔">
        </div>

        <div>
          <label class="text-text-secondary block mb-2">政策文件 *</label>
          <div class="upload-area rounded-lg p-8 text-center cursor-pointer" id="upload-area">
            <i class="fa fa-cloud-upload text-4xl text-text-tertiary mb-4"></i>
            <p class="text-text-secondary mb-2">点击选择文件或拖拽文件到此处</p>
            <p class="text-sm text-text-tertiary">支持 PDF、DOC、DOCX 格式，文件大小不超过 50MB</p>
            <input type="file" id="file-input" class="hidden" accept=".pdf,.doc,.docx">
          </div>
          <div id="file-list" class="mt-4 space-y-2"></div>
        </div>
      </form>

      <div class="flex justify-end space-x-4 mt-6">
        <button id="cancel-upload" class="px-6 py-3 bg-light text-text-secondary rounded-lg border border-border hover:bg-light/70 transition-colors">
          取消
        </button>
        <button id="confirm-upload" class="px-6 py-3 bg-primary text-white rounded-lg border border-primary hover:bg-primary/90 transition-colors">
          上传文件
        </button>
      </div>
    </div>
  </div>

  <!-- 页脚 -->
  <footer class="bg-lighter border-t border-border mt-12 py-6">
    <div class="container mx-auto px-6 text-center">
      <p class="text-text-secondary">© 2025 大模型能源电力政策信息挖掘系统 保留所有权利.</p>
    </div>
  </footer>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // 设置当前日期为默认值
      const today = new Date();
      const formattedDate = today.toISOString().split('T')[0];
      document.getElementById('publish-date').value = formattedDate;

      // 查询按钮事件
      document.getElementById('btn-search').addEventListener('click', function () {
        const searchParams = {
          title: document.getElementById('policy-title').value,
          unit: document.getElementById('issuing-unit').value,
          number: document.getElementById('document-number').value,
          level: document.getElementById('policy-level').value,
          date: document.getElementById('publish-date').value,
          keywords: document.getElementById('keywords').value
        };

        console.log('查询条件:', searchParams);
        alert('查询功能已触发，请查看控制台输出');
      });

      // 重置按钮事件
      document.getElementById('btn-reset').addEventListener('click', function () {
        document.getElementById('policy-title').value = '';
        document.getElementById('issuing-unit').value = '';
        document.getElementById('document-number').value = '';
        document.getElementById('policy-level').value = '';
        document.getElementById('publish-date').value = formattedDate;
        document.getElementById('keywords').value = '';
      });

      // 上传按钮事件
      document.getElementById('btn-upload').addEventListener('click', function () {
        const modal = document.getElementById('upload-modal');
        const modalContent = document.getElementById('upload-modal-content');

        modal.classList.remove('hidden');
        document.getElementById('upload-date').value = formattedDate;

        setTimeout(() => {
          modalContent.classList.remove('scale-95', 'opacity-0');
          modalContent.classList.add('scale-100', 'opacity-100');
        }, 10);
      });

      // 关闭上传模态框
      document.getElementById('close-upload-modal').addEventListener('click', closeUploadModal);
      document.getElementById('cancel-upload').addEventListener('click', closeUploadModal);

      // 文件上传区域
      const uploadArea = document.getElementById('upload-area');
      const fileInput = document.getElementById('file-input');
      const fileList = document.getElementById('file-list');

      uploadArea.addEventListener('click', () => fileInput.click());

      uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
      });

      uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
      });

      uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        handleFiles(e.dataTransfer.files);
      });

      fileInput.addEventListener('change', (e) => {
        handleFiles(e.target.files);
      });

      function handleFiles(files) {
        fileList.innerHTML = '';
        Array.from(files).forEach(file => {
          const fileItem = document.createElement('div');
          fileItem.className = 'flex items-center justify-between bg-light border border-border rounded-lg p-3';
          fileItem.innerHTML = `
            <div class="flex items-center">
              <i class="fa fa-file-text text-primary mr-3"></i>
              <div>
                <div class="font-medium text-text-primary">${file.name}</div>
                <div class="text-sm text-text-secondary">${(file.size / 1024 / 1024).toFixed(2)} MB</div>
              </div>
            </div>
            <button class="text-danger hover:text-danger/80 transition-colors" onclick="this.parentElement.remove()">
              <i class="fa fa-times"></i>
            </button>
          `;
          fileList.appendChild(fileItem);
        });
      }

      // 确认上传
      document.getElementById('confirm-upload').addEventListener('click', function () {
        const title = document.getElementById('upload-title').value;
        const unit = document.getElementById('upload-unit').value;
        const level = document.getElementById('upload-level').value;
        const date = document.getElementById('upload-date').value;

        if (!title || !unit || !level || !date) {
          alert('请填写必填项：政策标题、发文单位、政策层级、发布日期');
          return;
        }

        if (fileList.children.length === 0) {
          alert('请选择要上传的政策文件');
          return;
        }

        console.log('上传政策文件:', {
          title,
          unit,
          number: document.getElementById('upload-number').value,
          level,
          date,
          keywords: document.getElementById('upload-keywords').value,
          files: Array.from(fileList.children).map(item => item.querySelector('.font-medium').textContent)
        });

        alert('政策文件上传成功！');
        closeUploadModal();
      });

      // 表格操作按钮事件
      document.addEventListener('click', function(e) {
        if (e.target.closest('.fa-eye')) {
          const row = e.target.closest('tr');
          const title = row.cells[1].querySelector('.font-medium').textContent;
          alert(`预览政策文件：${title}\n\n这里将打开政策文件的在线预览窗口`);
        }

        if (e.target.closest('.fa-download')) {
          const row = e.target.closest('tr');
          const title = row.cells[1].querySelector('.font-medium').textContent;
          alert(`下载政策文件：${title}`);
        }

        if (e.target.closest('.fa-edit')) {
          const row = e.target.closest('tr');
          const title = row.cells[1].querySelector('.font-medium').textContent;
          alert(`编辑政策文件信息：${title}`);
        }

        if (e.target.closest('.fa-trash')) {
          const row = e.target.closest('tr');
          const title = row.cells[1].querySelector('.font-medium').textContent;
          if (confirm(`确定要删除政策文件"${title}"吗？`)) {
            alert('政策文件已删除');
          }
        }
      });

      function closeUploadModal() {
        const modal = document.getElementById('upload-modal');
        const modalContent = document.getElementById('upload-modal-content');

        modalContent.classList.remove('scale-100', 'opacity-100');
        modalContent.classList.add('scale-95', 'opacity-0');

        setTimeout(() => {
          modal.classList.add('hidden');
          // 清空表单
          document.getElementById('upload-title').value = '';
          document.getElementById('upload-unit').value = '';
          document.getElementById('upload-number').value = '';
          document.getElementById('upload-level').value = '';
          document.getElementById('upload-date').value = '';
          document.getElementById('upload-keywords').value = '';
          fileList.innerHTML = '';
          fileInput.value = '';
        }, 300);
      }

      // 滚动效果
      window.addEventListener('scroll', function () {
        const header = document.querySelector('header');
        if (window.scrollY > 10) {
          header.classList.add('shadow-md');
        } else {
          header.classList.remove('shadow-md');
        }
      });
    });
  </script>
</body>

</html>
