<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>******* 影响因子数据建模及分析</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#31969A',
            secondary: '#80CCE3',
            accent: '#8199C7',
            warning: '#E5CE66',
            danger: '#E74C3C',
            success: '#10B981',
            light: '#F8FAFC',
            lighter: '#FFFFFF',
            border: '#E2E8F0',
            grid: 'rgba(226, 232, 240, 0.5)',
            text: {
              primary: '#1E293B',
              secondary: '#64748B',
              tertiary: '#94A3B8'
            }
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
        },
      }
    }
  </script>
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .card-shadow {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
      }
      .scrollbar-thin {
        scrollbar-width: thin;
      }
      .scrollbar-thin::-webkit-scrollbar {
        width: 6px;
      }
      .scrollbar-thin::-webkit-scrollbar-thumb {
        background-color: rgba(100, 116, 139, 0.3);
        border-radius: 3px;
      }
      .stat-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(49, 150, 154, 0.1);
      }
      .modeling-badge {
        background: linear-gradient(135deg, #31969A, #80CCE3);
        animation: pulse 2s infinite;
      }
      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.8; }
      }
      .chart-container {
        position: relative;
        height: 300px;
        width: 100%;
      }
      .variable-item {
        transition: all 0.3s ease;
      }
      .variable-item:hover {
        background-color: rgba(49, 150, 154, 0.05);
      }
      .variable-item.selected {
        background-color: rgba(49, 150, 154, 0.1);
        border-color: #31969A;
      }
      .model-step {
        position: relative;
      }
      .model-step::before {
        content: '';
        position: absolute;
        left: 1rem;
        top: 3rem;
        bottom: -1rem;
        width: 2px;
        background: linear-gradient(to bottom, #31969A, transparent);
      }
      .model-step:last-child::before {
        display: none;
      }
    }
  </style>
</head>

<body class="bg-light font-inter text-text-primary min-h-screen">
  <!-- 顶部导航栏 -->
  <header class="bg-lighter/90 backdrop-blur-md border-b border-border sticky top-0 z-50 transition-all duration-300">
    <div class="container mx-auto px-6 py-3 flex justify-between items-center">
      <div class="flex items-center space-x-3">
        <i class="fa fa-flask text-primary text-3xl"></i>
        <div>
          <h1 class="text-lg font-semibold text-text-primary">******* 影响因子数据建模及分析</h1>
          <p class="text-sm text-text-secondary">基于多源数据的村级发展关键因子识别与量化分析</p>
        </div>
      </div>
      <div class="flex items-center space-x-4">
        <div class="modeling-badge text-white px-3 py-1 rounded-full text-sm">
          <i class="fa fa-cogs mr-1"></i> 模型运算中
        </div>
        <div class="bg-light/70 border border-border rounded-lg px-4 py-2 flex items-center">
          <i class="fa fa-calendar-o mr-2 text-text-secondary"></i>
          <span class="text-text-primary">2025年8月5日</span>
        </div>
      </div>
    </div>
  </header>

  <div class="container mx-auto px-6 py-6">
    <!-- 建模概览统计 -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-6">
      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">数据源</p>
            <h3 class="text-3xl font-bold mt-2 text-primary">4</h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center text-primary">
            <i class="fa fa-database text-xl"></i>
          </div>
        </div>
        <div class="text-text-tertiary text-sm">电力+经济+人口+地理</div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">变量总数</p>
            <h3 class="text-3xl font-bold mt-2 text-secondary">28</h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-secondary/10 flex items-center justify-center text-secondary">
            <i class="fa fa-list text-xl"></i>
          </div>
        </div>
        <div class="text-text-tertiary text-sm">已选择15个</div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">模型数量</p>
            <h3 class="text-3xl font-bold mt-2 text-accent">3</h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-accent/10 flex items-center justify-center text-accent">
            <i class="fa fa-sitemap text-xl"></i>
          </div>
        </div>
        <div class="text-text-tertiary text-sm">相关性+回归+聚类</div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">模型精度</p>
            <h3 class="text-3xl font-bold mt-2 text-success">92.3%</h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-success/10 flex items-center justify-center text-success">
            <i class="fa fa-check-circle text-xl"></i>
          </div>
        </div>
        <div class="text-text-tertiary text-sm">R² = 0.923</div>
      </div>

      <div class="bg-lighter border border-border rounded-xl p-5 card-shadow stat-card">
        <div class="flex justify-between items-start mb-4">
          <div>
            <p class="text-text-secondary">关键因子</p>
            <h3 class="text-3xl font-bold mt-2 text-warning">8</h3>
          </div>
          <div class="h-12 w-12 rounded-full bg-warning/10 flex items-center justify-center text-warning">
            <i class="fa fa-star text-xl"></i>
          </div>
        </div>
        <div class="text-text-tertiary text-sm">显著性 p<0.05</div>
      </div>
    </div>

    <!-- 建模流程 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
      <!-- 数据导入 -->
      <div class="bg-lighter border border-border rounded-xl p-6 card-shadow">
        <h3 class="text-xl font-semibold text-text-primary mb-4 flex items-center">
          <i class="fa fa-upload text-primary mr-2"></i>
          数据导入
        </h3>
        <div class="space-y-4">
          <div class="model-step">
            <div class="flex items-center p-3 bg-light rounded-lg border border-border">
              <div class="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">1</div>
              <div>
                <div class="font-medium">电力数据</div>
                <div class="text-sm text-text-secondary">用电量、负荷、电费等</div>
              </div>
              <div class="ml-auto">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-success/10 text-success">
                  <i class="fa fa-check mr-1"></i> 已导入
                </span>
              </div>
            </div>
          </div>
          <div class="model-step">
            <div class="flex items-center p-3 bg-light rounded-lg border border-border">
              <div class="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">2</div>
              <div>
                <div class="font-medium">经济数据</div>
                <div class="text-sm text-text-secondary">GDP、收入、产业结构等</div>
              </div>
              <div class="ml-auto">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-success/10 text-success">
                  <i class="fa fa-check mr-1"></i> 已导入
                </span>
              </div>
            </div>
          </div>
          <div class="model-step">
            <div class="flex items-center p-3 bg-light rounded-lg border border-border">
              <div class="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">3</div>
              <div>
                <div class="font-medium">人口数据</div>
                <div class="text-sm text-text-secondary">人口规模、结构、流动等</div>
              </div>
              <div class="ml-auto">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-success/10 text-success">
                  <i class="fa fa-check mr-1"></i> 已导入
                </span>
              </div>
            </div>
          </div>
          <div class="model-step">
            <div class="flex items-center p-3 bg-light rounded-lg border border-border">
              <div class="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">4</div>
              <div>
                <div class="font-medium">地理数据</div>
                <div class="text-sm text-text-secondary">地形、交通、资源等</div>
              </div>
              <div class="ml-auto">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-success/10 text-success">
                  <i class="fa fa-check mr-1"></i> 已导入
                </span>
              </div>
            </div>
          </div>
        </div>
        <button class="w-full mt-4 px-4 py-3 bg-primary text-white rounded-lg border border-primary hover:bg-primary/90 transition-colors">
          <i class="fa fa-plus mr-2"></i> 导入新数据源
        </button>
      </div>

      <!-- 变量选择 -->
      <div class="bg-lighter border border-border rounded-xl p-6 card-shadow">
        <h3 class="text-xl font-semibold text-text-primary mb-4 flex items-center">
          <i class="fa fa-check-square text-primary mr-2"></i>
          变量选择
        </h3>
        <div class="space-y-3 max-h-80 overflow-y-auto scrollbar-thin">
          <div class="variable-item selected p-3 bg-light rounded-lg border border-primary cursor-pointer">
            <div class="flex items-center justify-between">
              <div>
                <div class="font-medium">产业用电量</div>
                <div class="text-sm text-text-secondary">工业+农业用电总量</div>
              </div>
              <input type="checkbox" checked class="rounded border-border">
            </div>
          </div>
          <div class="variable-item selected p-3 bg-light rounded-lg border border-primary cursor-pointer">
            <div class="flex items-center justify-between">
              <div>
                <div class="font-medium">人均收入</div>
                <div class="text-sm text-text-secondary">农村居民人均可支配收入</div>
              </div>
              <input type="checkbox" checked class="rounded border-border">
            </div>
          </div>
          <div class="variable-item selected p-3 bg-light rounded-lg border border-primary cursor-pointer">
            <div class="flex items-center justify-between">
              <div>
                <div class="font-medium">人口密度</div>
                <div class="text-sm text-text-secondary">每平方公里人口数</div>
              </div>
              <input type="checkbox" checked class="rounded border-border">
            </div>
          </div>
          <div class="variable-item p-3 bg-light rounded-lg border border-border cursor-pointer">
            <div class="flex items-center justify-between">
              <div>
                <div class="font-medium">交通便利度</div>
                <div class="text-sm text-text-secondary">到县城距离指数</div>
              </div>
              <input type="checkbox" class="rounded border-border">
            </div>
          </div>
          <div class="variable-item p-3 bg-light rounded-lg border border-border cursor-pointer">
            <div class="flex items-center justify-between">
              <div>
                <div class="font-medium">教育水平</div>
                <div class="text-sm text-text-secondary">平均受教育年限</div>
              </div>
              <input type="checkbox" class="rounded border-border">
            </div>
          </div>
        </div>
        <div class="mt-4 pt-3 border-t border-border">
          <div class="text-sm text-text-secondary">已选择 15/28 个变量</div>
          <div class="w-full bg-light rounded-full h-2 mt-2">
            <div class="bg-primary h-2 rounded-full" style="width: 53.6%"></div>
          </div>
        </div>
      </div>

      <!-- 模型构建 -->
      <div class="bg-lighter border border-border rounded-xl p-6 card-shadow">
        <h3 class="text-xl font-semibold text-text-primary mb-4 flex items-center">
          <i class="fa fa-cogs text-primary mr-2"></i>
          模型构建
        </h3>
        <div class="space-y-4">
          <div class="p-4 bg-light rounded-lg border border-border">
            <div class="flex items-center justify-between mb-2">
              <div class="font-medium">相关性分析</div>
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-success/10 text-success">
                <i class="fa fa-check mr-1"></i> 完成
              </span>
            </div>
            <div class="text-sm text-text-secondary">Pearson相关系数矩阵</div>
            <div class="mt-2">
              <div class="text-xs text-text-tertiary">运行时间: 2.3秒</div>
            </div>
          </div>
          <div class="p-4 bg-light rounded-lg border border-border">
            <div class="flex items-center justify-between mb-2">
              <div class="font-medium">多元回归分析</div>
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-success/10 text-success">
                <i class="fa fa-check mr-1"></i> 完成
              </span>
            </div>
            <div class="text-sm text-text-secondary">线性回归+逐步回归</div>
            <div class="mt-2">
              <div class="text-xs text-text-tertiary">R² = 0.923, p < 0.001</div>
            </div>
          </div>
          <div class="p-4 bg-light rounded-lg border border-warning">
            <div class="flex items-center justify-between mb-2">
              <div class="font-medium">聚类分析</div>
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-warning/10 text-warning">
                <i class="fa fa-clock-o mr-1"></i> 运行中
              </span>
            </div>
            <div class="text-sm text-text-secondary">K-means聚类</div>
            <div class="mt-2">
              <div class="w-full bg-light rounded-full h-1">
                <div class="bg-warning h-1 rounded-full" style="width: 75%"></div>
              </div>
            </div>
          </div>
        </div>
        <button class="w-full mt-4 px-4 py-3 bg-accent text-white rounded-lg border border-accent hover:bg-accent/90 transition-colors">
          <i class="fa fa-play mr-2"></i> 运行新模型
        </button>
      </div>
    </div>

    <!-- 分析结果 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
      <!-- 影响因子贡献度 -->
      <div class="bg-lighter border border-border rounded-xl p-6 card-shadow">
        <h3 class="text-xl font-semibold text-text-primary mb-4 flex items-center">
          <i class="fa fa-bar-chart text-primary mr-2"></i>
          影响因子贡献度排名
        </h3>
        <div class="chart-container">
          <canvas id="contribution-chart"></canvas>
        </div>
      </div>

      <!-- 显著性分析 -->
      <div class="bg-lighter border border-border rounded-xl p-6 card-shadow">
        <h3 class="text-xl font-semibold text-text-primary mb-4 flex items-center">
          <i class="fa fa-line-chart text-primary mr-2"></i>
          显著性水平分析
        </h3>
        <div class="chart-container">
          <canvas id="significance-chart"></canvas>
        </div>
      </div>
    </div>

    <!-- 关键因子详细分析 -->
    <div class="bg-lighter border border-border rounded-xl p-6 card-shadow mb-6">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold text-text-primary">关键影响因子详细分析</h3>
        <div class="flex space-x-3">
          <button class="px-4 py-2 bg-secondary/20 text-secondary rounded-lg border border-secondary/30 hover:bg-secondary/30 transition-colors">
            <i class="fa fa-download mr-1"></i> 导出报告
          </button>
          <button class="px-4 py-2 bg-accent/20 text-accent rounded-lg border border-accent/30 hover:bg-accent/30 transition-colors">
            <i class="fa fa-refresh mr-1"></i> 重新计算
          </button>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table class="w-full border-collapse border border-border">
          <thead>
            <tr class="bg-light">
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">排名</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">影响因子</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">数据类型</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">贡献度</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">相关系数</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">显著性(p值)</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">置信区间</th>
              <th class="border border-border px-4 py-3 text-left text-text-primary font-semibold">影响方向</th>
            </tr>
          </thead>
          <tbody>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center justify-center w-8 h-8 bg-primary text-white rounded-full font-bold">1</span>
              </td>
              <td class="border border-border px-4 py-3 font-medium">产业用电量</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-primary/10 text-primary">
                  <i class="fa fa-bolt mr-1"></i> 电力数据
                </span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">23.5%</span>
              </td>
              <td class="border border-border px-4 py-3">0.847</td>
              <td class="border border-border px-4 py-3">
                <span class="text-success font-bold">< 0.001</span>
              </td>
              <td class="border border-border px-4 py-3">[0.782, 0.912]</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-success/10 text-success">
                  <i class="fa fa-arrow-up mr-1"></i> 正向
                </span>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center justify-center w-8 h-8 bg-secondary text-white rounded-full font-bold">2</span>
              </td>
              <td class="border border-border px-4 py-3 font-medium">人均收入水平</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-success/10 text-success">
                  <i class="fa fa-money mr-1"></i> 经济数据
                </span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">18.7%</span>
              </td>
              <td class="border border-border px-4 py-3">0.763</td>
              <td class="border border-border px-4 py-3">
                <span class="text-success font-bold">< 0.001</span>
              </td>
              <td class="border border-border px-4 py-3">[0.689, 0.837]</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-success/10 text-success">
                  <i class="fa fa-arrow-up mr-1"></i> 正向
                </span>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center justify-center w-8 h-8 bg-accent text-white rounded-full font-bold">3</span>
              </td>
              <td class="border border-border px-4 py-3 font-medium">人口密度</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-accent/10 text-accent">
                  <i class="fa fa-users mr-1"></i> 人口数据
                </span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">15.2%</span>
              </td>
              <td class="border border-border px-4 py-3">0.692</td>
              <td class="border border-border px-4 py-3">
                <span class="text-success font-bold">< 0.001</span>
              </td>
              <td class="border border-border px-4 py-3">[0.598, 0.786]</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-success/10 text-success">
                  <i class="fa fa-arrow-up mr-1"></i> 正向
                </span>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center justify-center w-8 h-8 bg-warning text-white rounded-full font-bold">4</span>
              </td>
              <td class="border border-border px-4 py-3 font-medium">交通便利度</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-warning/10 text-warning">
                  <i class="fa fa-road mr-1"></i> 地理数据
                </span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">12.8%</span>
              </td>
              <td class="border border-border px-4 py-3">0.634</td>
              <td class="border border-border px-4 py-3">
                <span class="text-success font-bold">< 0.01</span>
              </td>
              <td class="border border-border px-4 py-3">[0.521, 0.747]</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-success/10 text-success">
                  <i class="fa fa-arrow-up mr-1"></i> 正向
                </span>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center justify-center w-8 h-8 bg-text-secondary text-white rounded-full font-bold">5</span>
              </td>
              <td class="border border-border px-4 py-3 font-medium">教育水平</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-accent/10 text-accent">
                  <i class="fa fa-graduation-cap mr-1"></i> 人口数据
                </span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">9.6%</span>
              </td>
              <td class="border border-border px-4 py-3">0.578</td>
              <td class="border border-border px-4 py-3">
                <span class="text-success font-bold">< 0.01</span>
              </td>
              <td class="border border-border px-4 py-3">[0.456, 0.700]</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-success/10 text-success">
                  <i class="fa fa-arrow-up mr-1"></i> 正向
                </span>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center justify-center w-8 h-8 bg-text-secondary text-white rounded-full font-bold">6</span>
              </td>
              <td class="border border-border px-4 py-3 font-medium">生活用电量</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-primary/10 text-primary">
                  <i class="fa fa-home mr-1"></i> 电力数据
                </span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">8.3%</span>
              </td>
              <td class="border border-border px-4 py-3">0.521</td>
              <td class="border border-border px-4 py-3">
                <span class="text-success font-bold">< 0.05</span>
              </td>
              <td class="border border-border px-4 py-3">[0.389, 0.653]</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-success/10 text-success">
                  <i class="fa fa-arrow-up mr-1"></i> 正向
                </span>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center justify-center w-8 h-8 bg-text-secondary text-white rounded-full font-bold">7</span>
              </td>
              <td class="border border-border px-4 py-3 font-medium">产业结构多样性</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-success/10 text-success">
                  <i class="fa fa-pie-chart mr-1"></i> 经济数据
                </span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">7.1%</span>
              </td>
              <td class="border border-border px-4 py-3">0.467</td>
              <td class="border border-border px-4 py-3">
                <span class="text-success font-bold">< 0.05</span>
              </td>
              <td class="border border-border px-4 py-3">[0.325, 0.609]</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-success/10 text-success">
                  <i class="fa fa-arrow-up mr-1"></i> 正向
                </span>
              </td>
            </tr>
            <tr class="hover:bg-light/50 transition-colors">
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center justify-center w-8 h-8 bg-text-secondary text-white rounded-full font-bold">8</span>
              </td>
              <td class="border border-border px-4 py-3 font-medium">地形复杂度</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-warning/10 text-warning">
                  <i class="fa fa-mountain mr-1"></i> 地理数据
                </span>
              </td>
              <td class="border border-border px-4 py-3">
                <span class="text-primary font-bold">4.8%</span>
              </td>
              <td class="border border-border px-4 py-3">-0.412</td>
              <td class="border border-border px-4 py-3">
                <span class="text-success font-bold">< 0.05</span>
              </td>
              <td class="border border-border px-4 py-3">[-0.564, -0.260]</td>
              <td class="border border-border px-4 py-3">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-danger/10 text-danger">
                  <i class="fa fa-arrow-down mr-1"></i> 负向
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页信息 -->
      <div class="flex justify-between items-center mt-4 pt-4 border-t border-border">
        <div class="text-text-secondary">
          共 8 个关键因子，显示全部
        </div>
        <div class="text-sm text-text-secondary">
          模型解释度：92.3% | 调整R²：0.918 | F统计量：156.7 (p < 0.001)
        </div>
      </div>
    </div>
  </div>

  <!-- 页脚 -->
  <footer class="bg-lighter border-t border-border mt-12 py-6">
    <div class="container mx-auto px-6 text-center">
      <p class="text-text-secondary">© 2025 影响因子数据建模及分析系统 保留所有权利.</p>
    </div>
  </footer>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // 影响因子贡献度图表
      const contributionCtx = document.getElementById('contribution-chart').getContext('2d');
      const contributionChart = new Chart(contributionCtx, {
        type: 'bar',
        data: {
          labels: ['产业用电量', '人均收入', '人口密度', '交通便利度', '教育水平', '生活用电量', '产业多样性', '地形复杂度'],
          datasets: [{
            label: '贡献度 (%)',
            data: [23.5, 18.7, 15.2, 12.8, 9.6, 8.3, 7.1, 4.8],
            backgroundColor: [
              '#31969A',
              '#80CCE3',
              '#8199C7',
              '#E5CE66',
              '#10B981',
              '#31969A',
              '#80CCE3',
              '#E74C3C'
            ],
            borderWidth: 0,
            borderRadius: 4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            },
            tooltip: {
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              titleColor: '#1E293B',
              bodyColor: '#64748B',
              borderColor: 'rgba(49, 150, 154, 0.2)',
              borderWidth: 1,
              callbacks: {
                label: function(context) {
                  return `贡献度: ${context.raw}%`;
                }
              }
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: '贡献度 (%)'
              }
            },
            x: {
              ticks: {
                maxRotation: 45
              }
            }
          }
        }
      });

      // 显著性水平图表
      const significanceCtx = document.getElementById('significance-chart').getContext('2d');
      const significanceChart = new Chart(significanceCtx, {
        type: 'scatter',
        data: {
          datasets: [{
            label: '显著因子',
            data: [
              {x: 23.5, y: 0.001},
              {x: 18.7, y: 0.001},
              {x: 15.2, y: 0.001},
              {x: 12.8, y: 0.008},
              {x: 9.6, y: 0.012},
              {x: 8.3, y: 0.032},
              {x: 7.1, y: 0.041},
              {x: 4.8, y: 0.048}
            ],
            backgroundColor: '#31969A',
            borderColor: '#31969A',
            pointRadius: 6,
            pointHoverRadius: 8
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            },
            tooltip: {
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              titleColor: '#1E293B',
              bodyColor: '#64748B',
              borderColor: 'rgba(49, 150, 154, 0.2)',
              borderWidth: 1,
              callbacks: {
                title: function() {
                  return '影响因子';
                },
                label: function(context) {
                  return [
                    `贡献度: ${context.raw.x}%`,
                    `p值: ${context.raw.y}`
                  ];
                }
              }
            }
          },
          scales: {
            x: {
              title: {
                display: true,
                text: '贡献度 (%)'
              }
            },
            y: {
              type: 'logarithmic',
              title: {
                display: true,
                text: 'p值 (对数尺度)'
              },
              min: 0.001,
              max: 0.1
            }
          }
        }
      });

      // 变量选择交互
      document.querySelectorAll('.variable-item').forEach(item => {
        item.addEventListener('click', function() {
          const checkbox = this.querySelector('input[type="checkbox"]');
          checkbox.checked = !checkbox.checked;

          if (checkbox.checked) {
            this.classList.add('selected');
            this.classList.remove('border-border');
            this.classList.add('border-primary');
          } else {
            this.classList.remove('selected');
            this.classList.remove('border-primary');
            this.classList.add('border-border');
          }

          // 更新选择计数
          const selectedCount = document.querySelectorAll('.variable-item input:checked').length;
          const totalCount = document.querySelectorAll('.variable-item').length;
          const progressBar = document.querySelector('.bg-primary');
          const countText = document.querySelector('.text-text-secondary');

          countText.textContent = `已选择 ${selectedCount}/${totalCount} 个变量`;
          progressBar.style.width = `${(selectedCount / totalCount) * 100}%`;
        });
      });
    });
  </script>
</body>

</html>
